# Projeto Kotlin

Um projeto básico em Kotlin usando Gradle.

## Estrutura do Projeto

```
mobile-test/
├── src/
│   ├── main/kotlin/
│   │   └── Main.kt
│   └── test/kotlin/
│       └── MainTest.kt
├── build.gradle.kts
├── settings.gradle.kts
├── gradle.properties
└── README.md
```

## Como Executar

### Executar o projeto:

```bash ./gradlew run

```

### Executar os testes:

```bash
./gradlew test
```

### Compilar o projeto:

```bash
./gradlew build
```

## Características

-   **Linguagem**: Kotlin 1.9.10
-   **JDK**: Java 8+
-   **Build Tool**: Gradle
-   **Testes**: JUnit Platform com kotlin-test

## Próximos Passos

1. Adicione mais classes e funcionalidades no diretório `src/main/kotlin/`
2. Escreva testes correspondentes em `src/test/kotlin/`
3. Configure dependências adicionais no `build.gradle.kts` conforme necessário
